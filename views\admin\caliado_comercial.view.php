<?php
#region region DOCS
/** @var AliadoComercial|null $aliado The fetched AliadoComercial object, or null if not found/error */
/** @var PDO $conexion PDO connection (likely available from preparar.php) */
/** @var ClientePotencial[] $clientes_potenciales */

// Use the AliadoComercial class namespace
use App\classes\AliadoComercial;
use App\classes\ClientePotencial;

// Start session if not already started (might be handled in preparar.php, but good practice)
if (session_status() === PHP_SESSION_NONE) {
	session_start();
}

// Note: Feedback variables ($success_text, $error_text etc.) might be initialized
// in preparar.php. This view doesn't directly use them but relies on the controller
// setting flash messages which might be displayed via JS in core_js_adm.view.php

#endregion DOCS
?>
<!DOCTYPE html>
<html lang="es" class="dark-mode"> <?php // Match laliados_comerciales.view.php ?>
<head>
	<meta charset="utf-8"/>
	<title><?php echo APP_NAME; ?> | Perfil Aliado Comercial</title> <?php // Updated title ?>
	<meta content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no" name="viewport"/>
	<meta content="Detalle del perfil del aliado comercial" name="description"/> <?php // Updated description ?>
	<meta content="" name="author"/>

	<?php #region HEAD ?>
	<?php // Include the common admin head elements (CSS, etc.) ?>
	<?php require_once __ROOT__ . '/views/admin/general/head.view.php'; ?>
	<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.5/font/bootstrap-icons.css">
	<style>
        /* Specific styles for profile details within the admin panel */
        .profile-section {
            margin-bottom: 1.5rem;
            padding-bottom: 1.5rem;
            border-bottom: 1px solid var(--bs-border-color); /* Use theme variable */
        }

        .profile-section:last-child {
            border-bottom: none;
            margin-bottom: 0;
            padding-bottom: 0;
        }

        .profile-section h5 {
            margin-bottom: 1rem;
            color: var(--bs-primary); /* Use theme variable */
            font-size: 1.1rem; /* Slightly larger heading */
            font-weight: 600;
        }

        .profile-label {
            font-weight: 600; /* Bolder label */
            color: var(--bs-body-color); /* Theme text color */
            opacity: 0.8; /* Slightly muted */
        }

        .profile-value {
            color: var(--bs-body-color); /* Theme text color */
        }

        .profile-value a {
            word-break: break-all; /* Ensure long URLs wrap */
            color: var(--bs-link-color);
        }

        .profile-value a:hover {
            color: var(--bs-link-hover-color);
        }

        .profile-value .badge {
            margin-right: 5px;
            margin-bottom: 5px;
        }
        
        /* Tab styles */
        .nav-tabs .nav-link {
            color: var(--bs-body-color);
            border-bottom: 0;
        }
        
        .nav-tabs .nav-link.active {
            color: var(--bs-primary);
            font-weight: 600;
            border-bottom: 0;
        }
        
        .nav-tabs .nav-link:hover:not(.active) {
            border-color: transparent;
            color: var(--bs-primary);
        }
        
        /* Project details styles */
        .project-details {
            background-color: var(--bs-body-bg);
            border-radius: 0.25rem;
            padding: 1.25rem;
        }
        
        .project-list-item {
            transition: background-color 0.2s;
        }
        
        .project-list-item:hover {
            background-color: rgba(var(--bs-primary-rgb), 0.05);
        }
        
        .project-title {
            color: var(--bs-primary);
            font-weight: 600;
        }
        
        .project-date {
            font-size: 0.9rem;
            color: var(--bs-secondary);
        }
	</style>
	<?php #endregion HEAD ?>
</head>
<body>
<!-- BEGIN #loader -->
<div id="loader" class="app-loader">
	<span class="spinner"></span>
</div>
<!-- END #loader -->

<!-- BEGIN #app -->
<div id="app" class="app app-header-fixed app-sidebar-fixed">
	<!-- #header -->
	<?php require_once __ROOT__ . '/views/admin/general/header.view.php'; ?>

	<!-- #sidebar -->
	<?php require_once __ROOT__ . '/views/admin/general/sidebar.view.php'; ?>

	<!-- BEGIN #content -->
	<div id="content" class="app-content">

		<?php if ($aliado): ?>
			<?php #region region PAGE HEADER ?>
			<div class="d-flex align-items-center mb-3">
				<div>
					<h4 class="mb-0">
						Perfil de Aliado Comercial: <?php echo htmlspecialchars($aliado->getNombreRazonSocial() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
					</h4>
					<p class="mb-0 text-muted">Detalles del perfil profesional y de contacto.</p>
				</div>
				<div class="ms-auto">
					<a href="laliados_comerciales" class="btn btn-secondary">
						<i class="fa fa-arrow-left fa-fw me-1"></i> Volver a pendientes
					</a>
					<a href="laliados_comerciales_viables" class="btn btn-secondary">
						<i class="fa fa-arrow-left fa-fw me-1"></i> Volver a viables
					</a>
				</div>
			</div>
			<hr>
			<?php #endregion PAGE HEADER ?>


			<?php #region region TABS NAVIGATION ?>
			<!-- Tabs Navigation -->
			<ul class="nav nav-tabs nav-tabs-v2" id="profileTabs" role="tablist">
				<li class="nav-item me-2" role="presentation">
					<a href="#details-tab" class="nav-link active" id="details-link" data-bs-toggle="tab" role="tab" aria-controls="details-tab" aria-selected="true">
						<i class="fa fa-user fa-fw me-1"></i> Detalles del Perfil
					</a>
				</li>
				<?php if ($aliado->getViable() === 1): // Only show Projects tab if ally is viable ?>
				<li class="nav-item" role="presentation">
					<a href="#projects-tab" class="nav-link" id="projects-link" data-bs-toggle="tab" role="tab" aria-controls="projects-tab" aria-selected="false">
						<i class="fa fa-briefcase fa-fw me-1"></i> Proyecto Asociado 
					</a>
				</li>
				<?php endif; ?>
			</ul>

			<!-- Tabs Content -->
			<div class="tab-content pt-3" id="profileTabsContent">
				<!-- Details Tab (Active by Default) -->
				<div class="tab-pane fade show active" id="details-tab" role="tabpanel" aria-labelledby="details-link">
					<div class="panel panel-inverse no-border-radious mb-0">
						<div class="panel-heading no-border-radious d-flex justify-content-between align-items-center">
							<h4 class="panel-title">Detalles del Perfil</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
							</div>
						</div>
						<div class="panel-body">
							<!-- Información del Aliado Comercial Section -->
							<section class="profile-section" id="aliado-info">
								<h5><i class="bi bi-person-fill me-2"></i>Información del Aliado Comercial</h5>
								<div class="row mb-2">
									<div class="col-md-3 profile-label">Nombre Completo / Razón Social:</div>
									<div class="col-md-9 profile-value"><?php echo htmlspecialchars($aliado->getNombreRazonSocial() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
								</div>
								<div class="row mb-2">
									<div class="col-md-3 profile-label">Cédula / NIT:</div>
									<div class="col-md-9 profile-value"><?php echo htmlspecialchars($aliado->getCedulaNit() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
								</div>
								<div class="row mb-2">
									<div class="col-md-3 profile-label">Teléfono de Contacto:</div>
									<div class="col-md-9 profile-value"><?php echo htmlspecialchars($aliado->getTelefonoContacto() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
								</div>
								<div class="row mb-2">
									<div class="col-md-3 profile-label">Correo Electrónico:</div>
									<div class="col-md-9 profile-value">
										<a href="mailto:<?php echo htmlspecialchars($aliado->getCorreoElectronico() ?? '', ENT_QUOTES, 'UTF-8'); ?>">
											<?php echo htmlspecialchars($aliado->getCorreoElectronico() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
										</a>
									</div>
								</div>
								<div class="row mb-2">
									<div class="col-md-3 profile-label">Ciudad de Operación:</div>
									<div class="col-md-9 profile-value"><?php echo htmlspecialchars($aliado->getCiudadOperacion() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
								</div>
								<div class="row mb-2">
									<div class="col-md-3 profile-label">País de Operación:</div>
									<div class="col-md-9 profile-value"><?php echo htmlspecialchars($aliado->getPaisOperacion() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
								</div>
								<div class="row mb-2">
									<div class="col-md-3 profile-label">Tipo de Alianza:</div>
									<div class="col-md-9 profile-value"><?php echo htmlspecialchars($aliado->getTipoAlianza() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
								</div>
							</section>

							<!-- Información del Cliente Potencial Section -->
							<section class="profile-section" id="cliente-potencial-info">
								<h5><i class="bi bi-building me-2"></i>Información del Cliente Potencial</h5>
								<?php
								// Get the cliente potencial object - Assume it's loaded with the aliado
								$clientePotencial = $aliado->getClientePotencial();
								?>
								<?php if ($clientePotencial): ?>
									<div class="row mb-2">
										<div class="col-md-3 profile-label">Nombre de la Empresa:</div>
										<div class="col-md-9 profile-value"><?php echo htmlspecialchars($clientePotencial->getNombreEmpresa() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
									</div>
									<div class="row mb-2">
										<div class="col-md-3 profile-label">Nombre del Contacto:</div>
										<div class="col-md-9 profile-value"><?php echo htmlspecialchars($clientePotencial->getNombreContacto() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
									</div>
									<div class="row mb-2">
										<div class="col-md-3 profile-label">Cargo del Contacto:</div>
										<div class="col-md-9 profile-value"><?php echo htmlspecialchars($clientePotencial->getCargoContacto() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
									</div>
									<div class="row mb-2">
										<div class="col-md-3 profile-label">Teléfono del Contacto:</div>
										<div class="col-md-9 profile-value"><?php echo htmlspecialchars($clientePotencial->getTelefonoContacto() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
									</div>
									<div class="row mb-2">
										<div class="col-md-3 profile-label">Correo del Contacto:</div>
										<div class="col-md-9 profile-value">
											<a href="mailto:<?php echo htmlspecialchars($clientePotencial->getCorreoContacto() ?? '', ENT_QUOTES, 'UTF-8'); ?>">
												<?php echo htmlspecialchars($clientePotencial->getCorreoContacto() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?>
											</a>
										</div>
									</div>
									<div class="row mb-2">
										<div class="col-md-3 profile-label">Ciudad del Cliente:</div>
										<div class="col-md-9 profile-value"><?php echo htmlspecialchars($clientePotencial->getCiudad() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
									</div>
									<div class="row mb-2">
										<div class="col-md-3 profile-label">País del Cliente:</div>
										<div class="col-md-9 profile-value"><?php echo htmlspecialchars($clientePotencial->getPais() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
									</div>
									<div class="row mb-2">
										<div class="col-md-3 profile-label">Descripción del Negocio:</div>
										<div class="col-md-9 profile-value"><?php echo nl2br(htmlspecialchars($clientePotencial->getDescripcionNegocio() ?? 'N/A', ENT_QUOTES, 'UTF-8')); ?></div>
									</div>

								<?php else: ?>
									<p class="text-muted"><i>No se encontró información del cliente potencial para este aliado.</i></p>
								<?php endif; ?>
							</section>
						</div>
					</div>
				</div>

				<?php if ($aliado->getViable() === 1): ?>
				<!-- Projects Tab -->
				<div class="tab-pane fade" id="projects-tab" role="tabpanel" aria-labelledby="projects-link">
					<div class="panel panel-inverse no-border-radious mb-0">
						<div class="panel-heading no-border-radious d-flex justify-content-between align-items-center">
							<h4 class="panel-title">Proyecto Asociado a este Aliado</h4>
							<div class="panel-heading-btn">
								<a href="javascript:;" class="btn btn-xs btn-icon btn-default" data-toggle="panel-expand"><i class="fa fa-expand"></i></a>
								<a href="javascript:;" class="btn btn-xs btn-icon btn-warning" data-toggle="panel-collapse"><i class="fa fa-minus"></i></a>
							</div>
						</div>
						<div class="panel-body">
							<?php if ($proyecto_asociado): ?>
								<div class="row mb-2">
									<div class="col-md-3 profile-label">Descripción:</div>
									<div class="col-md-9 profile-value"><?php echo htmlspecialchars($proyecto_asociado->getDescripcion() ?? 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
								</div>
								<div class="row mb-2">
									<div class="col-md-3 profile-label">Fecha Inicio:</div>
									<div class="col-md-9 profile-value"><?php echo htmlspecialchars($proyecto_asociado->getFechaInicio() ? $proyecto_asociado->getFechaInicio() : 'N/A', ENT_QUOTES, 'UTF-8'); ?></div>
								</div>
								<div class="mt-3">
									<a href="eproyecto?id=<?php echo $proyecto_asociado->getId(); ?>" class="btn btn-sm btn-primary" title="Editar Proyecto">
										<i class="fa fa-edit"></i> Editar Proyecto
									</a>
								</div>
							<?php else: ?>
								<div class="alert alert-info">No hay ningún proyecto activo asociado a este aliado comercial viable.</div>
								<div class="mt-4 text-end border-top pt-3">
									<a href="iproyecto_aliado_comercial?aliado_id=<?php echo $aliado->getId(); ?>" class="btn btn-success">
										<i class="fa fa-plus fa-fw me-1"></i> Crear Nuevo Proyecto para este Aliado
									</a>
								</div>
							<?php endif; ?>
						</div>
					</div>
				</div>
				<?php endif; ?>
			</div>
			<?php #endregion TABS NAVIGATION ?>

		<?php else: ?>
			<?php #region region ERROR MESSAGE ?>
			<div class="alert alert-danger d-flex align-items-center">
				<i class="fa fa-exclamation-triangle fa-2x me-3"></i>
				<div>
					<strong>Error:</strong> No se pudo cargar la información del aliado comercial o el ID no es válido.
					<a href="laliados_comerciales" class="alert-link ms-2">Volver a la lista</a>.
				</div>
			</div>
			<?php #endregion ERROR MESSAGE ?>
		<?php endif; ?>

	</div>
	<!-- END #content -->

	<!-- BEGIN scroll-top-btn -->
	<a href="#" class="btn btn-icon btn-circle btn-success btn-scroll-to-top" data-toggle="scroll-to-top"><i class="fa fa-angle-up"></i></a>
	<!-- END scroll-top-btn -->

</div>
<!-- END #app -->

<?php #region SCRIPTS ?>
<?php // Include the core JS files (jQuery, Bootstrap, SweetAlert, etc.) ?>
<?php require_once __ROOT__ . '/views/admin/general/core_js_adm.view.php'; ?>
<!-- Tab functionality script -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize Bootstrap tabs
    var triggerTabList = [].slice.call(document.querySelectorAll('#profileTabs a'))
    triggerTabList.forEach(function (triggerEl) {
        var tabTrigger = new bootstrap.Tab(triggerEl)
        triggerEl.addEventListener('click', function (event) {
            event.preventDefault()
            tabTrigger.show()
        })
    })
    
    // Handle URL hash for direct tab access
    if (window.location.hash) {
        const hash = window.location.hash;
        if (hash === '#projects') {
            const projectsTab = document.querySelector('#projects-link');
            if (projectsTab) {
                const tab = new bootstrap.Tab(projectsTab);
                tab.show();
            }
        }
    }
});
</script>
<?php #endregion SCRIPTS ?>

</body>
</html>
